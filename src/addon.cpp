#include <node.h>
#include <v8.h>
#include <memory>
#include <fstream>
#include <iostream>
#include "stream-factory/iq_stream_factory.h"
#include "video-converter/iq_video_stream_processor.h"
#include "node/config_helpers.h"

namespace VideoDecodingAddon {

using v8::Context;
using v8::Function;
using v8::FunctionCallbackInfo;
using v8::Isolate;
using v8::Local;
using v8::Object;
using v8::String;
using v8::Value;
using v8::Array;
using v8::Number;
using v8::Boolean;
using v8::Exception;
using v8::Persistent;

// Single IQ video stream processor instance (simplified single-instance design)
static std::unique_ptr<IQVideoStream::StreamProcessor> g_iqVideoProcessor;

/**
 * Parse stream configuration from JavaScript object
 */
IQStreamFactory::StreamConfig parseStreamConfig(Isolate* isolate, Local<Object> sourceObj) {
    IQStreamFactory::StreamConfig config;
    Local<Context> context = isolate->GetCurrentContext();

    // Get type
    Local<Value> typeVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "type").ToLocalChecked()).ToLocalChecked();
    if (typeVal->IsString()) {
        v8::String::Utf8Value typeStr(isolate, typeVal);
        config.type = std::string(*typeStr);
    }

    if (config.type == "wav") {
        // WAV-specific parameters
        Local<Value> pathVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "path").ToLocalChecked()).ToLocalChecked();
        if (pathVal->IsString()) {
            v8::String::Utf8Value pathStr(isolate, pathVal);
            config.wav.filePath = std::string(*pathStr);
        }

        Local<Value> playModeVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "playMode").ToLocalChecked()).ToLocalChecked();
        if (playModeVal->IsString()) {
            v8::String::Utf8Value playModeStr(isolate, playModeVal);
            config.wav.playMode = std::string(*playModeStr);
        } else {
            config.wav.playMode = "realtime";  // default
        }

    } else if (config.type == "bladerf") {
        // BladeRF-specific parameters
        Local<Value> serialVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "serial").ToLocalChecked()).ToLocalChecked();
        if (serialVal->IsString()) {
            v8::String::Utf8Value serialStr(isolate, serialVal);
            config.bladerf.serial = std::string(*serialStr);
        }

        Local<Value> channelVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "channel").ToLocalChecked()).ToLocalChecked();
        if (channelVal->IsNumber()) {
            config.bladerf.channel = channelVal->Uint32Value(context).FromJust();
        }

        Local<Value> sampleRateVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "sampleRate").ToLocalChecked()).ToLocalChecked();
        if (sampleRateVal->IsNumber()) {
            config.bladerf.sampleRate = sampleRateVal->Uint32Value(context).FromJust();
        }

        Local<Value> centerHzVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "centerHz").ToLocalChecked()).ToLocalChecked();
        if (centerHzVal->IsNumber()) {
            config.bladerf.centerHz = centerHzVal->NumberValue(context).FromJust();
        }

        Local<Value> bandwidthVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "bandwidth").ToLocalChecked()).ToLocalChecked();
        if (bandwidthVal->IsNumber()) {
            config.bladerf.bandwidth = bandwidthVal->NumberValue(context).FromJust();
        }
    }

    return config;
}

/**
 * Create IQ video processor function
 * Usage: createIQVideoProcessor(config, frameCallback)
 */
void CreateIQVideoProcessor(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    Local<Context> context = isolate->GetCurrentContext();

    // Validate arguments
    if (args.Length() < 2 || !args[0]->IsObject() || !args[1]->IsFunction()) {
        isolate->ThrowException(Exception::TypeError(String::NewFromUtf8(isolate, "Usage: createIQVideoProcessor(config, frameCallback)").ToLocalChecked()));
        return;
    }

    try {
        // Stop any existing processor (single-instance design)
        if (g_iqVideoProcessor) {
            g_iqVideoProcessor->stop();
            g_iqVideoProcessor.reset();
        }

        Local<Object> configObj = args[0].As<Object>();

        // Parse stream configuration
        Local<Value> sourceVal = configObj->Get(context, String::NewFromUtf8(isolate, "source").ToLocalChecked()).ToLocalChecked();
        if (!sourceVal->IsObject()) {
            isolate->ThrowException(Exception::TypeError(
                String::NewFromUtf8(isolate, "Config must have 'source' object").ToLocalChecked()));
            return;
        }

        // Parse video processing configuration
        Local<Value> processingVal = configObj->Get(context, String::NewFromUtf8(isolate, "processing").ToLocalChecked()).ToLocalChecked();
        if (!processingVal->IsObject()) {
            isolate->ThrowException(Exception::TypeError(String::NewFromUtf8(isolate, "Config must have 'processing' object").ToLocalChecked()));
            return;
        }

        auto streamConfig = parseStreamConfig(isolate, sourceVal.As<Object>());

        // Create IQ stream using factory (early validation in Node.js thread)
        auto iqStream = IQStreamFactory::createStream(streamConfig);
        if (!iqStream) {
            std::string error = "Failed to create IQ stream: " + IQStreamFactory::lastError();
            isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, error.c_str()).ToLocalChecked()));
            return;
        }

        auto processingConfig = NodeHelpers::Config::parseVideoProcessingConfig(isolate, processingVal.As<Object>());
        if (std::string errorMsg; !processingConfig.isValid(errorMsg)) {
            std::string error = "Failed to parse IQ processing config: " + errorMsg;
            isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, error.c_str()).ToLocalChecked()));
            return;
        }

        // Create frame callback (for now, just a placeholder)
        auto frameCallback = [](const uint8_t* frameData, size_t frameSize) {
            // TODO: Call JavaScript callback
            std::cout << "Frame received: " << frameSize << " bytes" << std::endl;
        };

        auto exceptionCallback = [](const std::string& error) {
            // TODO: Handle exceptions in JavaScript context
            std::cerr << "IQ video processor error: " << error << std::endl;
        };

        // Create IQ video processor
        g_iqVideoProcessor = std::make_unique<IQVideoStream::StreamProcessor>(std::move(iqStream), std::move(processingConfig), frameCallback, exceptionCallback);

        // Start IQ video processing
        if (!g_iqVideoProcessor->start()) {
            std::string error = "Failed to start IQ video processor: " + g_iqVideoProcessor->lastError();
            g_iqVideoProcessor.reset();
            isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, error.c_str()).ToLocalChecked()));
            return;
        }

        // Return success (simplified interface - no handle ID needed)
        Local<Object> result = Object::New(isolate);
        result->Set(context, String::NewFromUtf8(isolate, "success").ToLocalChecked(), Boolean::New(isolate, true)).Check();

        args.GetReturnValue().Set(result);
    } catch (const std::exception& e) {
        isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, ("IQ video processor error: " + std::string(e.what())).c_str()).ToLocalChecked()));
    }
}

/**
 * Stop IQ video processor function
 */
void StopIQVideoProcessor(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();

    if (g_iqVideoProcessor) {
        g_iqVideoProcessor->stop();
        g_iqVideoProcessor.reset();
        args.GetReturnValue().Set(Boolean::New(isolate, true));
    } else {
        args.GetReturnValue().Set(Boolean::New(isolate, false));
    }
}

/**
 * Get IQ video processor stats function
 */
void GetIQVideoProcessorStats(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();

    if (g_iqVideoProcessor) {
        std::string stats = g_iqVideoProcessor->getStats();
        args.GetReturnValue().Set(String::NewFromUtf8(isolate, stats.c_str()).ToLocalChecked());
    } else {
        args.GetReturnValue().Set(String::NewFromUtf8(isolate, "{}").ToLocalChecked());
    }
}

/**
 * Check if IQ video processor is running
 */
void IsIQVideoProcessorRunning(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();

    bool running = g_iqVideoProcessor && g_iqVideoProcessor->isRunning();
    args.GetReturnValue().Set(Boolean::New(isolate, running));
}

/**
 * Hello World function for testing
 */
void HelloWorld(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    args.GetReturnValue().Set(String::NewFromUtf8(isolate, "Hello from Video Decoding Pipeline!").ToLocalChecked());
}

/**
 * Initialize the addon
 */
void Initialize(Local<Object> exports) {
    NODE_SET_METHOD(exports, "helloWorld", HelloWorld);
    NODE_SET_METHOD(exports, "createIQVideoProcessor", CreateIQVideoProcessor);
    NODE_SET_METHOD(exports, "stopIQVideoProcessor", StopIQVideoProcessor);
    NODE_SET_METHOD(exports, "getIQVideoProcessorStats", GetIQVideoProcessorStats);
    NODE_SET_METHOD(exports, "isIQVideoProcessorRunning", IsIQVideoProcessorRunning);
}

NODE_MODULE(NODE_GYP_MODULE_NAME, Initialize)

} // namespace VideoDecodingAddon
