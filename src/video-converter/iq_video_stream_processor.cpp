#include "iq_video_stream_processor.h"
#include <stdexcept>
#include <iostream>
#include <sstream>
#include <chrono>
#include <algorithm>
#include <utility>

namespace IQVideoStream {

StreamProcessor::StreamProcessor(std::unique_ptr<IIQStream> stream, ProcessingConfig config, const FrameCallback& frameCallback, const ExceptionCallback& exceptionCallback)
    : stream_(std::move(stream))
    , config_(std::move(config))
    , frameCallback_(frameCallback)
    , exceptionCallback_(exceptionCallback)
    , workerReady_(false)
    , workerStartupFailed_(false)
    , running_(false)
    , stopRequested_(false)
    , initialized_(false) {

    stats_.startTime = std::chrono::steady_clock::now();
}

StreamProcessor::~StreamProcessor() {
    stop();
}

bool StreamProcessor::start() {
    if (running_) {
        setError("IQ video stream processor is already running");
        return false;
    }

    // Validate stream
    if (!stream_ || !stream_->isActive()) {
        setError("IQ stream is not active: " + (stream_ ? stream_->lastError() : "null stream"));
        return false;
    }

    try {


        // Calculate acquisition configuration using stream sample rate
        acquisitionConfig_ = toAcquisitionConfig(config_);
        acquisitionConfig_.sampleRate = stream_->sampleRate();  // Use actual stream sample rate

        // Reset synchronization variables
        workerReady_ = false;
        workerStartupFailed_ = false;
        stopRequested_ = false;
        initialized_ = false;

        // Start worker thread with synchronous startup
        std::unique_lock<std::mutex> lock(startupMutex_);
        // workerThread_ = std::make_unique<std::thread>(&StreamProcessor::workerThreadFunc, this);
        workerThread_ = std::make_unique<std::thread>([this]() {
            try {
                workerThreadFunc();
            } catch (const std::exception& e) {
                setError("Worker thread exception: " + std::string(e.what()));
                workerStartupFailed_ = true;
                startupCondition_.notify_one();
            }
        });

        // Wait for worker thread to be ready or fail
        startupCondition_.wait(lock, [this] { return workerReady_.load() || workerStartupFailed_.load(); });

        if (workerStartupFailed_) {
            // Cleanup on failure
            if (workerThread_ && workerThread_->joinable()) {
                workerThread_->join();
            }
            workerThread_.reset();
            chunkProcessor_.reset();
            return false;
        }

        running_ = true;
        return true;

    } catch (const std::exception& e) {
        setError("Exception starting IQ video stream processor: " + std::string(e.what()));
        return false;
    }
}

void StreamProcessor::stop() {
    if (running_.exchange(false)) {
        stopRequested_ = true;

        // Wait for worker thread to finish
        if (workerThread_ && workerThread_->joinable()) {
            workerThread_->join();
        }
        workerThread_.reset();

        // Clean up processing components
        chunkProcessor_.reset();
        stream_.reset();
    }
}

bool StreamProcessor::isRunning() const {
    return running_.load();
}

std::string StreamProcessor::getStats() const {
    try {
        std::ostringstream json;
        json << "{"
             << "\"running\":" << (isRunning() ? "true" : "false") << ","
             << "\"totalSamplesRead\":" << stats_.totalSamplesRead << ","
             << "\"totalChunksProcessed\":" << stats_.totalChunksProcessed << ","
             << "\"streamErrors\":" << stats_.streamErrors
             << "}";

        return json.str();

    } catch (const std::exception& e) {
        return "{\"error\":\"" + std::string(e.what()) + "\"}";
    }
}

const std::string& StreamProcessor::lastError() const {
    return lastError_;
}

ProcessingHelpers::AcquisitionConfig StreamProcessor::toAcquisitionConfig(const ProcessingConfig& config) const {
    return ProcessingHelpers::AcquisitionConfig::calculateConfig(config.queueDepth.raw);
}

bool StreamProcessor::initializeAcquisition() {
    if (initialized_) {
        lastError_ = "Acquisition is already initialized";
        return false;
    }

    if (!stream_ || !stream_->isActive()) {
        lastError_ = "Stream is not active";
        return false;
    }

    // Calculate actual read chunk size if using auto strategy
    size_t actualReadChunkSize = ProcessingHelpers::calculateSliceSize(
        acquisitionConfig_.sampleRate,
        acquisitionConfig_.sliceStrategy,
        acquisitionConfig_.readChunkSize
    );

    try {
        // Create chunk processor with calculated parameters
        chunkProcessor_ = std::make_unique<ChunkProcessor<SampleType>>(
            acquisitionConfig_.writeChunkSize,
            actualReadChunkSize,
            acquisitionConfig_.readOverlapSize,
            acquisitionConfig_.numWriteChunks,
            [this](SampleType* data, size_t size) { onChunkReady(data, size); }
        );

        initialized_ = true;
        return true;
    } catch (const std::exception& e) {
        lastError_ = "Failed to initialize chunk processor: " + std::string(e.what());
        return false;
    }
}

bool StreamProcessor::processIteration() {
    if (!initialized_) {
        lastError_ = "Acquisition not initialized";
        return false;
    }

    if (!stream_->isActive()) {
        return false;  // End of stream
    }

    // Get write buffer from chunk processor
    SampleType* writePtr = chunkProcessor_->getWriteChunkPtr();

    // Read samples from stream
    bool success = stream_->readSamples(writePtr, acquisitionConfig_.writeChunkSize);

    if (success) {
        stats_.totalSamplesRead += acquisitionConfig_.writeChunkSize;
        // Commit the chunk - this may trigger read callbacks
        chunkProcessor_->commitWriteChunk();
        return true;
    } else {
        if (stream_->isActive()) {
            // Stream error (not EOS)
            stats_.streamErrors++;
            lastError_ = "Stream read error: " + stream_->lastError();
        }
        return false;
    }
}

bool StreamProcessor::canContinue() const {
    return initialized_ && stream_ && stream_->isActive();
}

void StreamProcessor::workerThreadFunc() {
    try {
        // Initialize the acquisition components
        if (!initializeAcquisition()) {
            setError("Failed to initialize acquisition: " + lastError_);
            workerStartupFailed_ = true;
            startupCondition_.notify_one();
            return;
        }

        // Signal that worker is ready
        {
            std::lock_guard<std::mutex> lock(startupMutex_);
            workerReady_ = true;
        }
        startupCondition_.notify_one();

        // Main processing loop
        while (!stopRequested_ && canContinue()) {
            if (!processIteration()) {
                break;
            }
        }

        // Close stream when done
        if (stream_) {
            stream_->close();
        }

    } catch (const std::exception& e) {
        setError("Worker thread exception: " + std::string(e.what()));
        workerStartupFailed_ = true;
        startupCondition_.notify_one();
    }
}

void StreamProcessor::onChunkReady(SampleType* data, size_t size) {
    stats_.totalChunksProcessed++;
    handleProcessedChunk(data, size);
}

void StreamProcessor::handleProcessedChunk(SampleType* data, size_t size) {
    // For now, output to stdout as specified in strategy document
    // TODO: Implement actual video processing pipeline (stages 3-6)
    std::cout << "Processed chunk: " << size << " samples" << std::endl;

    // TODO: When video processing is implemented, call frameCallback_ with frame data
    // frameCallback_(frameData, frameSize);
}

void StreamProcessor::setError(const std::string& error) {
    lastError_ = error;
}

} // namespace IQVideoStream
