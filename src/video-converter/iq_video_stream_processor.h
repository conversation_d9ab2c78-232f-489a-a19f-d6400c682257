#pragma once

#include "../chunk-processor/chunk_processor.h"
#include "../iiq-stream/iiq_stream.h"
#include "../types.h"
#include "./processing_config.h"
#include "./processing_helpers.h"
#include <atomic>
#include <condition_variable>
#include <functional>
#include <memory>
#include <mutex>
#include <string>
#include <thread>

namespace IQVideoStream {
/**
 * Manages the complete IQ-to-video demodulation process for a single stream.
 * Takes an initialized IIQStream and orchestrates video processing.
 */
class StreamProcessor {
public:
    using FrameCallback = std::function<void(const uint8_t* frameData, size_t frameSize)>;
    using ExceptionCallback = std::function<void(const std::string& error)>;

    /**
     * Constructor
     * @param stream Initialized and validated IIQStream (takes ownership)
     * @param config Video processing configuration
     * @param frameCallback Callback for processed video frames (Called when a complete video frame is ready)
     * @param exceptionCallback Callback for handling exceptions (Called on errors in processing)
     */
    StreamProcessor(std::unique_ptr<IIQStream> stream, ProcessingConfig  config, const FrameCallback& frameCallback, const ExceptionCallback& exceptionCallback);
    ~StreamProcessor();
    // Disable copy and assignment
    StreamProcessor(const StreamProcessor&) = delete;
    StreamProcessor& operator=(const StreamProcessor&) = delete;

    /**
     * Initialize and start video conversion with synchronous thread startup
     * Blocks until the worker thread is fully initialized and ready
     * @return true if successful, false otherwise
     */
    bool start();
    void stop();
    bool isRunning() const;
    
    /**
     * Get conversion statistics as JSON string
     * @return Statistics JSON or empty object on error
     */
    std::string getStats() const;
    
    /**
     * Get the last error message
     * @return Last error message or empty string if no error
     */
    const std::string& lastError() const;

private:
    // Configuration and callback
    ProcessingConfig config_;
    const FrameCallback& frameCallback_;
    const ExceptionCallback& exceptionCallback_;

    // Processing components
    std::unique_ptr<IIQStream> stream_;
    std::unique_ptr<ChunkProcessor<SampleType>> chunkProcessor_;
    ProcessingHelpers::AcquisitionConfig acquisitionConfig_;

    // Thread management
    std::unique_ptr<std::thread> workerThread_;
    std::mutex startupMutex_;
    std::condition_variable startupCondition_;
    std::atomic<bool> workerReady_;
    std::atomic<bool> workerStartupFailed_;

    // State management
    std::atomic<bool> running_;
    std::atomic<bool> stopRequested_;
    std::atomic<bool> initialized_;
    std::string lastError_;

    // Statistics
    struct Stats {
        uint64_t totalSamplesRead;
        uint64_t totalChunksProcessed;
        uint64_t streamErrors;
        std::chrono::steady_clock::time_point startTime;

        Stats() : totalSamplesRead(0), totalChunksProcessed(0), streamErrors(0) {}
    };
    mutable Stats stats_;
    
    /**
     * Convert VideoProcessingConfig to AcquisitionConfig
     * @param config Video processing configuration
     * @return Acquisition configuration
     */
    ProcessingHelpers::AcquisitionConfig toAcquisitionConfig(const ProcessingConfig& config) const;

    /**
     * Initialize the acquisition components
     * @return true if initialized successfully, false otherwise
     */
    bool initializeAcquisition();

    /**
     * Process one iteration of the acquisition loop
     * @return true if should continue, false if should stop
     */
    bool processIteration();

    /**
     * Check if the acquisition can continue processing
     */
    bool canContinue() const;

    /**
     * Worker thread main function
     */
    void workerThreadFunc();

    /**
     * Chunk processor callback - forwards to handleProcessedChunk
     */
    void onChunkReady(SampleType* data, size_t size);

    /**
     * Handle processed chunks from acquisition worker
     * Currently outputs to stdout as specified in strategy
     * @param data Processed chunk data
     * @param size Size of chunk in samples
     */
    void handleProcessedChunk(SampleType* data, size_t size);

    /**
     * Set error message
     * @param error Error message to set
     */
    void setError(const std::string& error);
};

} // namespace IQVideoStream
